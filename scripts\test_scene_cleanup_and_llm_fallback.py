#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试场景清除和LLM故障转移功能
验证自动提取场景的清除逻辑和LLM服务的故障转移机制
"""

import os
import sys
import json
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_scene_cleanup():
    """测试场景清除功能"""
    print("🔧 测试场景清除功能")
    print("=" * 50)
    
    try:
        from src.utils.character_scene_manager import CharacterSceneManager
        
        # 创建临时目录用于测试
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建数据库目录
            db_dir = os.path.join(temp_dir, 'src', 'character_scene_db')
            os.makedirs(db_dir, exist_ok=True)

            # 初始化管理器
            manager = CharacterSceneManager(temp_dir)

            # 创建测试场景数据
            test_scenes = {
                "scenes": {
                    "user_scene_1": {
                        "name": "用户创建的场景1",
                        "description": "这是用户手动创建的场景",
                        "created_at": "2025-06-26 09:00:00"
                    },
                    "auto_云溪村_2025-06-26 09_20_35": {
                        "name": "云溪村",
                        "description": "自动提取的场景",
                        "created_at": "2025-06-26 09:20:35"
                    },
                    "auto_阿明家_2025-06-26 09_20_35": {
                        "name": "阿明家",
                        "description": "自动提取的场景",
                        "created_at": "2025-06-26 09:20:35"
                    },
                    "镜头场景_测试场景": {
                        "name": "镜头场景",
                        "description": "旧版本自动生成的场景",
                        "created_at": "2025-06-25 20:00:00"
                    },
                    "user_scene_2": {
                        "name": "用户创建的场景2",
                        "description": "这是另一个用户手动创建的场景",
                        "created_at": "2025-06-26 08:00:00"
                    }
                },
                "last_updated": "2025-06-26 09:20:35"
            }
            
            # 保存测试数据
            scenes_file = os.path.join(db_dir, "scenes.json")
            with open(scenes_file, 'w', encoding='utf-8') as f:
                json.dump(test_scenes, f, ensure_ascii=False, indent=2)
            
            print("📝 测试数据准备完成")
            print(f"原始场景数量: {len(test_scenes['scenes'])}")
            for scene_id, scene_data in test_scenes['scenes'].items():
                scene_type = "自动提取" if scene_id.startswith(("auto_", "镜头场景_")) else "用户创建"
                print(f"  - {scene_id}: {scene_data['name']} ({scene_type})")
            
            # 执行清除操作
            print(f"\n🧹 执行场景清除...")
            print(f"场景文件路径: {scenes_file}")
            print(f"文件是否存在: {os.path.exists(scenes_file)}")
            manager._clear_auto_extracted_data()
            
            # 检查清除结果
            with open(scenes_file, 'r', encoding='utf-8') as f:
                updated_scenes = json.load(f)
            
            print(f"\n📊 清除结果:")
            print(f"清除后场景数量: {len(updated_scenes['scenes'])}")
            
            remaining_scenes = []
            removed_scenes = []
            
            for scene_id in test_scenes['scenes']:
                if scene_id in updated_scenes['scenes']:
                    remaining_scenes.append(scene_id)
                else:
                    removed_scenes.append(scene_id)
            
            print(f"\n✅ 保留的场景 ({len(remaining_scenes)}个):")
            for scene_id in remaining_scenes:
                scene_data = updated_scenes['scenes'][scene_id]
                print(f"  - {scene_id}: {scene_data['name']}")
            
            print(f"\n🗑️ 清除的场景 ({len(removed_scenes)}个):")
            for scene_id in removed_scenes:
                scene_data = test_scenes['scenes'][scene_id]
                print(f"  - {scene_id}: {scene_data['name']}")
            
            # 验证清除逻辑
            expected_removed = ["auto_云溪村_2025-06-26 09_20_35", "auto_阿明家_2025-06-26 09_20_35", "镜头场景_测试场景"]
            expected_remaining = ["user_scene_1", "user_scene_2"]
            
            success = True
            
            for scene_id in expected_removed:
                if scene_id in updated_scenes['scenes']:
                    print(f"❌ 错误: 应该被清除的场景仍然存在: {scene_id}")
                    success = False
            
            for scene_id in expected_remaining:
                if scene_id not in updated_scenes['scenes']:
                    print(f"❌ 错误: 不应该被清除的场景被误删: {scene_id}")
                    success = False
            
            if success:
                print(f"\n🎉 场景清除功能测试通过!")
                print("✅ 正确清除了以'auto_'和'镜头场景_'开头的场景")
                print("✅ 正确保留了用户创建的场景")
                return True
            else:
                print(f"\n❌ 场景清除功能测试失败!")
                return False
                
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_llm_fallback():
    """测试LLM故障转移功能"""
    print("\n🔧 测试LLM故障转移功能")
    print("=" * 50)
    
    try:
        from src.services.llm_service import LLMService
        from src.core.api_manager import APIManager

        # 创建API管理器和LLM服务实例
        api_manager = APIManager()
        llm_service = LLMService(api_manager)
        
        print("📝 测试故障转移方法是否存在...")
        
        # 检查是否有故障转移方法
        if hasattr(llm_service, 'execute_with_fallback'):
            print("✅ execute_with_fallback 方法存在")
        else:
            print("❌ execute_with_fallback 方法不存在")
            return False
        
        print("\n📝 测试方法调用...")
        
        # 测试简单的调用（不实际执行，只检查方法签名）
        import inspect
        sig = inspect.signature(llm_service.execute_with_fallback)
        expected_params = ['prompt', 'max_tokens', 'temperature', 'preferred_provider']

        actual_params = list(sig.parameters.keys())  # 包含所有参数

        print(f"方法参数: {actual_params}")

        for param in expected_params:
            if param in actual_params:
                print(f"✅ 参数 {param} 存在")
            else:
                print(f"❌ 参数 {param} 缺失")
                return False
        
        print(f"\n🎉 LLM故障转移功能测试通过!")
        print("✅ execute_with_fallback 方法正确实现")
        print("✅ 方法参数签名正确")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_character_scene_manager_integration():
    """测试角色场景管理器的集成"""
    print("\n🔧 测试角色场景管理器集成")
    print("=" * 50)
    
    try:
        from src.utils.character_scene_manager import CharacterSceneManager
        
        # 创建临时目录用于测试
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = CharacterSceneManager(temp_dir)
            
            print("📝 检查故障转移方法...")
            
            if hasattr(manager, '_execute_llm_with_fallback'):
                print("✅ _execute_llm_with_fallback 方法存在")
            else:
                print("❌ _execute_llm_with_fallback 方法不存在")
                return False
            
            print("✅ 角色场景管理器集成测试通过!")
            return True
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始测试场景清除和LLM故障转移功能")
    
    os.chdir(project_root)
    
    # 执行所有测试
    tests = [
        ("场景清除功能", test_scene_cleanup),
        ("LLM故障转移功能", test_llm_fallback),
        ("角色场景管理器集成", test_character_scene_manager_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"测试: {test_name}")
        print(f"{'='*60}")
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行失败: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print(f"\n{'='*60}")
    print("📋 测试结果总结")
    print(f"{'='*60}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过! 场景清除和LLM故障转移功能正常工作")
        return 0
    else:
        print("⚠️ 部分测试失败，需要检查相关功能")
        return 1

if __name__ == "__main__":
    sys.exit(main())
