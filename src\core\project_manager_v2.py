"""
优化后的项目管理器 - 重新设计数据结构
"""

import os
import json
import shutil
from datetime import datetime
from typing import Dict, List, Optional, Any
from src.utils.logger import logger


class ProjectManagerV2:
    """优化后的项目管理器"""
    
    def __init__(self, projects_dir: str = "output"):
        self.projects_dir = os.path.abspath(projects_dir)
        self.current_project = None
        self.current_project_path = None
        
        # 确保项目目录存在
        os.makedirs(self.projects_dir, exist_ok=True)
        
        logger.info(f"项目管理器V2初始化，项目保存目录: {self.projects_dir}")
    
    def create_project(self, project_name: str, description: str = "") -> bool:
        """创建新项目"""
        try:
            # 创建项目目录
            project_dir = os.path.join(self.projects_dir, project_name)
            os.makedirs(project_dir, exist_ok=True)
            
            # 创建子目录
            subdirs = ["audio", "images", "videos", "subtitles", "texts", "exports"]
            for subdir in subdirs:
                os.makedirs(os.path.join(project_dir, subdir), exist_ok=True)
            
            # 创建优化后的项目数据结构
            project_data = {
                # 基础信息
                "project_info": {
                    "name": project_name,
                    "description": description,
                    "created_time": datetime.now().isoformat(),
                    "last_modified": datetime.now().isoformat(),
                    "version": "2.0",
                    "project_dir": project_dir
                },
                
                # 内容数据
                "content": {
                    "original_text": "",
                    "rewritten_text": "",
                    "five_stage_data": {}
                },
                
                # 镜头数据 - 统一管理所有镜头信息
                "shots": {},
                
                # 全局设置
                "settings": {
                    "voice": {
                        "provider": "edge_tts",
                        "voice": "zh-CN-YunxiNeural",
                        "speed": 1.0,
                        "max_segment_duration": 5.0  # 最大语音段时长5秒
                    },
                    "image": {
                        "engine": "pollinations",
                        "style": "realistic",
                        "quality": "high",
                        "resolution": "1024x1024"
                    },
                    "video": {
                        "engine": "cogvideox_flash",
                        "duration": 5.0,
                        "fps": 30,
                        "width": 1024,
                        "height": 1024,
                        "motion_intensity": 0.5,
                        "concurrent_tasks": 3
                    },
                    "composition": {
                        "resolution": "1920x1080",
                        "fps": 30,
                        "format": "mp4",
                        "quality": "high",
                        "transition_type": "fade",
                        "transition_duration": 0.5
                    }
                },
                
                # 生成进度
                "progress": {
                    "voice": {"total": 0, "completed": 0, "failed": 0},
                    "image": {"total": 0, "completed": 0, "failed": 0},
                    "video": {"total": 0, "completed": 0, "failed": 0},
                    "composition": {"status": "pending", "completion_percentage": 0}
                },
                
                # 输出文件
                "outputs": {
                    "final_video": None,
                    "preview_video": None,
                    "audio_track": None
                }
            }
            
            # 保存项目文件
            project_file = os.path.join(project_dir, "project.json")
            with open(project_file, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, ensure_ascii=False, indent=2)
            
            self.current_project = project_data
            self.current_project_path = project_file
            
            logger.info(f"项目创建成功: {project_name}")
            return True
            
        except Exception as e:
            logger.error(f"创建项目失败: {e}")
            return False
    
    def load_project(self, project_path: str) -> bool:
        """加载项目"""
        try:
            if os.path.isdir(project_path):
                project_file = os.path.join(project_path, "project.json")
            else:
                project_file = project_path
            
            if not os.path.exists(project_file):
                logger.error(f"项目文件不存在: {project_file}")
                return False
            
            with open(project_file, 'r', encoding='utf-8') as f:
                project_data = json.load(f)
            
            # 检查是否为旧版本项目，如果是则升级
            if "version" not in project_data.get("project_info", {}):
                project_data = self._upgrade_project_structure(project_data)
                self._save_project(project_data, project_file)
            
            self.current_project = project_data
            self.current_project_path = project_file
            
            project_name = project_data.get("project_info", {}).get("name", "未知项目")
            logger.info(f"项目加载成功: {project_name}")
            return True
            
        except Exception as e:
            logger.error(f"加载项目失败: {e}")
            return False
    
    def _upgrade_project_structure(self, old_data: Dict) -> Dict:
        """升级旧版本项目结构到新版本"""
        logger.info("检测到旧版本项目，正在升级数据结构...")
        
        # 提取基础信息
        project_info = {
            "name": old_data.get("project_name", "未命名项目"),
            "description": old_data.get("description", ""),
            "created_time": old_data.get("created_time", datetime.now().isoformat()),
            "last_modified": datetime.now().isoformat(),
            "version": "2.0",
            "project_dir": old_data.get("project_dir", "")
        }
        
        # 提取内容数据
        content = {
            "original_text": old_data.get("original_text", ""),
            "rewritten_text": old_data.get("rewritten_text", ""),
            "five_stage_data": old_data.get("five_stage_storyboard", {})
        }
        
        # 转换镜头数据
        shots = self._convert_shots_data(old_data)
        
        # 提取设置
        settings = self._extract_settings(old_data)
        
        # 计算进度
        progress = self._calculate_progress(shots)
        
        # 构建新的数据结构
        new_data = {
            "project_info": project_info,
            "content": content,
            "shots": shots,
            "settings": settings,
            "progress": progress,
            "outputs": {
                "final_video": None,
                "preview_video": None,
                "audio_track": None
            }
        }
        
        logger.info("项目数据结构升级完成")
        return new_data
    
    def _convert_shots_data(self, old_data: Dict) -> Dict:
        """转换镜头数据到新格式 - 修复版本，从多个数据源提取完整镜头数据"""
        shots = {}

        # 🔧 修复：优先从五阶段分镜数据中提取镜头
        five_stage_data = old_data.get("five_stage_storyboard", {})
        stage_data = five_stage_data.get("stage_data", {})

        # 尝试从阶段5获取优化后的分镜数据
        stage5_data = stage_data.get("5", {})
        storyboard_results = stage5_data.get("storyboard_results", [])

        # 如果阶段5没有数据，尝试从阶段4获取
        if not storyboard_results:
            stage4_data = stage_data.get("4", {})
            storyboard_results = stage4_data.get("storyboard_results", [])

        # 从分镜结果中提取镜头数据
        shot_counter = 1
        if storyboard_results:
            for scene_result in storyboard_results:
                storyboard_script = scene_result.get("storyboard_script", "")
                scene_info = scene_result.get("scene_info", {})
                scene_name = scene_info.get("scene_name", f"场景{len(shots)//10 + 1}") if isinstance(scene_info, dict) else str(scene_info)

                # 解析分镜脚本中的镜头
                if storyboard_script:
                    script_shots = self._parse_shots_from_script(storyboard_script)
                    for script_shot in script_shots:
                        shot_id = f"镜头{shot_counter}"
                        shots[shot_id] = {
                            "id": shot_id,
                            "scene_id": scene_name,
                            "title": shot_id,
                            "description": script_shot.get("镜头原文", script_shot.get("original_text", "")),
                            "voice": {
                                "text": script_shot.get("镜头原文", script_shot.get("original_text", "")),
                                "audio_path": None,
                                "duration": 0,
                                "status": "pending"
                            },
                            "image": {
                                "path": None,
                                "status": "pending"
                            },
                            "video": {
                                "path": None,
                                "segments": [],
                                "status": "pending"
                            }
                        }
                        shot_counter += 1

        # 🔧 修复：如果分镜数据不足，从语音数据中补充
        voice_segments = old_data.get("voice_generation", {}).get("voice_segments", [])
        for i, segment in enumerate(voice_segments):
            shot_id = segment.get("shot_id", f"镜头{shot_counter}")

            # 如果这个镜头还没有被添加，则添加它
            if shot_id not in shots:
                shots[shot_id] = {
                    "id": shot_id,
                    "scene_id": segment.get("scene_id", ""),
                    "title": shot_id,
                    "description": segment.get("original_text", ""),
                    "voice": {
                        "text": segment.get("original_text", ""),
                        "audio_path": segment.get("audio_path"),
                        "duration": 0,  # 需要从音频文件获取
                        "status": segment.get("status", "pending")
                    },
                    "image": {
                        "path": None,
                        "status": "pending"
                    },
                    "video": {
                        "path": None,
                        "segments": [],
                        "status": "pending"
                    }
                }
                shot_counter += 1
            else:
                # 如果镜头已存在，更新语音信息
                shots[shot_id]["voice"]["audio_path"] = segment.get("audio_path")
                shots[shot_id]["voice"]["status"] = segment.get("status", "pending")

        return shots

    def _parse_shots_from_script(self, storyboard_script: str) -> List[Dict]:
        """从分镜脚本中解析镜头数据"""
        shots = []

        # 按行分割脚本
        lines = storyboard_script.split('\n')
        current_shot = {}

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检测镜头开始标记
            if any(keyword in line for keyword in ['分镜', '镜头', 'Shot']):
                # 保存上一个镜头
                if current_shot:
                    shots.append(current_shot)

                # 开始新镜头
                current_shot = {
                    "镜头原文": "",
                    "画面描述": "",
                    "台词/旁白": "",
                    "音效提示": ""
                }

            # 提取镜头内容
            if current_shot:
                if "镜头原文" in line or "原文" in line:
                    current_shot["镜头原文"] += line.replace("镜头原文:", "").replace("原文:", "").strip()
                elif "画面描述" in line or "画面" in line:
                    current_shot["画面描述"] += line.replace("画面描述:", "").replace("画面:", "").strip()
                elif "台词" in line or "旁白" in line:
                    current_shot["台词/旁白"] += line.replace("台词/旁白:", "").replace("台词:", "").replace("旁白:", "").strip()
                elif "音效" in line:
                    current_shot["音效提示"] += line.replace("音效提示:", "").replace("音效:", "").strip()
                else:
                    # 如果没有明确标识，添加到镜头原文中
                    if not any(current_shot.values()):
                        current_shot["镜头原文"] += line + " "

        # 保存最后一个镜头
        if current_shot:
            shots.append(current_shot)

        return shots

    def _extract_settings(self, old_data: Dict) -> Dict:
        """从旧数据中提取设置"""
        voice_gen = old_data.get("voice_generation", {})
        image_gen = old_data.get("image_generation", {})
        video_gen = old_data.get("video_generation", {})
        video_comp = old_data.get("video_composition", {})

        return {
            "voice": {
                "provider": voice_gen.get("provider", "edge_tts"),
                "voice": voice_gen.get("settings", {}).get("voice", "zh-CN-YunxiNeural"),
                "speed": voice_gen.get("settings", {}).get("speed", 1.0),
                "max_segment_duration": 5.0
            },
            "image": {
                "engine": image_gen.get("settings", {}).get("engine", "pollinations"),
                "style": image_gen.get("settings", {}).get("style", "realistic"),
                "quality": image_gen.get("settings", {}).get("quality", "high"),
                "resolution": image_gen.get("settings", {}).get("resolution", "1024x1024")
            },
            "video": {
                "engine": video_gen.get("settings", {}).get("engine", "cogvideox_flash"),
                "duration": video_gen.get("settings", {}).get("duration", 5.0),
                "fps": video_gen.get("settings", {}).get("fps", 30),
                "width": video_gen.get("settings", {}).get("width", 1024),
                "height": video_gen.get("settings", {}).get("height", 1024),
                "motion_intensity": video_gen.get("settings", {}).get("motion_intensity", 0.5),
                "concurrent_tasks": video_gen.get("settings", {}).get("concurrent_tasks", 3)
            },
            "composition": {
                "resolution": video_comp.get("settings", {}).get("resolution", "1920x1080"),
                "fps": video_comp.get("settings", {}).get("fps", 30),
                "format": video_comp.get("settings", {}).get("format", "mp4"),
                "quality": video_comp.get("settings", {}).get("quality", "high"),
                "transition_type": video_comp.get("settings", {}).get("transition_type", "fade"),
                "transition_duration": video_comp.get("settings", {}).get("transition_duration", 0.5)
            }
        }

    def _calculate_progress(self, shots: Dict) -> Dict:
        """计算各阶段进度"""
        total = len(shots)
        voice_completed = sum(1 for shot in shots.values() if shot["voice"]["status"] == "completed")
        image_completed = sum(1 for shot in shots.values() if shot["image"]["status"] == "completed")
        video_completed = sum(1 for shot in shots.values() if shot["video"]["status"] == "completed")

        return {
            "voice": {"total": total, "completed": voice_completed, "failed": 0},
            "image": {"total": total, "completed": image_completed, "failed": 0},
            "video": {"total": total, "completed": video_completed, "failed": 0},
            "composition": {"status": "pending", "completion_percentage": 0}
        }

    def save_project(self) -> bool:
        """保存当前项目"""
        if not self.current_project or not self.current_project_path:
            logger.error("没有当前项目可保存")
            return False

        try:
            # 更新最后修改时间
            self.current_project["project_info"]["last_modified"] = datetime.now().isoformat()

            return self._save_project(self.current_project, self.current_project_path)

        except Exception as e:
            logger.error(f"保存项目失败: {e}")
            return False

    def _save_project(self, project_data: Dict, project_file: str) -> bool:
        """保存项目数据到文件"""
        try:
            # 创建备份
            if os.path.exists(project_file):
                backup_file = project_file + ".backup"
                shutil.copy2(project_file, backup_file)

            # 保存项目文件
            with open(project_file, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, ensure_ascii=False, indent=2)

            logger.info(f"项目已保存: {project_file}")
            return True

        except Exception as e:
            logger.error(f"保存项目文件失败: {e}")
            return False

    def add_shot(self, shot_id: str, scene_id: str, title: str, description: str) -> bool:
        """添加新镜头"""
        if not self.current_project:
            logger.error("没有当前项目")
            return False

        try:
            shot_data = {
                "id": shot_id,
                "scene_id": scene_id,
                "title": title,
                "description": description,
                "voice": {
                    "text": description,
                    "audio_path": None,
                    "duration": 0,
                    "status": "pending"
                },
                "image": {
                    "path": None,
                    "status": "pending"
                },
                "video": {
                    "path": None,
                    "segments": [],
                    "status": "pending"
                }
            }

            self.current_project["shots"][shot_id] = shot_data

            # 更新进度统计
            self._update_progress()

            logger.info(f"镜头已添加: {shot_id}")
            return True

        except Exception as e:
            logger.error(f"添加镜头失败: {e}")
            return False

    def update_shot_voice(self, shot_id: str, audio_path: str, duration: float, status: str = "completed") -> bool:
        """更新镜头语音信息"""
        if not self.current_project or shot_id not in self.current_project["shots"]:
            logger.error(f"镜头不存在: {shot_id}")
            return False

        try:
            shot = self.current_project["shots"][shot_id]
            shot["voice"]["audio_path"] = audio_path
            shot["voice"]["duration"] = duration
            shot["voice"]["status"] = status

            self._update_progress()

            logger.info(f"镜头语音已更新: {shot_id}")
            return True

        except Exception as e:
            logger.error(f"更新镜头语音失败: {e}")
            return False

    def update_shot_image(self, shot_id: str, image_path: str, status: str = "completed") -> bool:
        """更新镜头图像信息"""
        if not self.current_project or shot_id not in self.current_project["shots"]:
            logger.error(f"镜头不存在: {shot_id}")
            return False

        try:
            shot = self.current_project["shots"][shot_id]
            shot["image"]["path"] = image_path
            shot["image"]["status"] = status

            self._update_progress()

            logger.info(f"镜头图像已更新: {shot_id}")
            return True

        except Exception as e:
            logger.error(f"更新镜头图像失败: {e}")
            return False

    def update_shot_video(self, shot_id: str, video_path: str, status: str = "completed") -> bool:
        """更新镜头视频信息"""
        if not self.current_project or shot_id not in self.current_project["shots"]:
            logger.error(f"镜头不存在: {shot_id}")
            return False

        try:
            shot = self.current_project["shots"][shot_id]
            shot["video"]["path"] = video_path
            shot["video"]["status"] = status

            self._update_progress()

            logger.info(f"镜头视频已更新: {shot_id}")
            return True

        except Exception as e:
            logger.error(f"更新镜头视频失败: {e}")
            return False

    def add_video_segment(self, shot_id: str, segment_path: str, segment_index: int = None) -> bool:
        """为镜头添加视频段"""
        if not self.current_project or shot_id not in self.current_project["shots"]:
            logger.error(f"镜头不存在: {shot_id}")
            return False

        try:
            shot = self.current_project["shots"][shot_id]
            segments = shot["video"]["segments"]

            segment_data = {
                "path": segment_path,
                "index": segment_index if segment_index is not None else len(segments),
                "created_time": datetime.now().isoformat()
            }

            segments.append(segment_data)

            logger.info(f"视频段已添加: {shot_id} -> {segment_path}")
            return True

        except Exception as e:
            logger.error(f"添加视频段失败: {e}")
            return False

    def _update_progress(self):
        """更新进度统计"""
        if not self.current_project:
            return

        shots = self.current_project["shots"]
        total = len(shots)

        voice_completed = sum(1 for shot in shots.values() if shot["voice"]["status"] == "completed")
        image_completed = sum(1 for shot in shots.values() if shot["image"]["status"] == "completed")
        video_completed = sum(1 for shot in shots.values() if shot["video"]["status"] == "completed")

        self.current_project["progress"] = {
            "voice": {"total": total, "completed": voice_completed, "failed": 0},
            "image": {"total": total, "completed": image_completed, "failed": 0},
            "video": {"total": total, "completed": video_completed, "failed": 0},
            "composition": {"status": "pending", "completion_percentage": 0}
        }

    def get_shots(self) -> Dict:
        """获取所有镜头数据"""
        if not self.current_project:
            return {}
        return self.current_project.get("shots", {})

    def get_shot(self, shot_id: str) -> Optional[Dict]:
        """获取指定镜头数据"""
        shots = self.get_shots()
        return shots.get(shot_id)

    def get_project_info(self) -> Dict:
        """获取项目基础信息"""
        if not self.current_project:
            return {}
        return self.current_project.get("project_info", {})

    def get_settings(self) -> Dict:
        """获取项目设置"""
        if not self.current_project:
            return {}
        return self.current_project.get("settings", {})

    def get_progress(self) -> Dict:
        """获取项目进度"""
        if not self.current_project:
            return {}
        return self.current_project.get("progress", {})

    def update_settings(self, category: str, settings: Dict) -> bool:
        """更新设置"""
        if not self.current_project:
            logger.error("没有当前项目")
            return False

        try:
            if category in self.current_project["settings"]:
                self.current_project["settings"][category].update(settings)
                logger.info(f"设置已更新: {category}")
                return True
            else:
                logger.error(f"未知设置类别: {category}")
                return False

        except Exception as e:
            logger.error(f"更新设置失败: {e}")
            return False

    def set_output_file(self, file_type: str, file_path: str) -> bool:
        """设置输出文件路径"""
        if not self.current_project:
            logger.error("没有当前项目")
            return False

        try:
            if file_type in self.current_project["outputs"]:
                self.current_project["outputs"][file_type] = file_path
                logger.info(f"输出文件已设置: {file_type} -> {file_path}")
                return True
            else:
                logger.error(f"未知输出文件类型: {file_type}")
                return False

        except Exception as e:
            logger.error(f"设置输出文件失败: {e}")
            return False

    def get_project_data(self) -> Optional[Dict]:
        """获取完整项目数据"""
        return self.current_project

    def split_long_voice_segments(self, max_duration: float = 5.0) -> bool:
        """分割过长的语音段"""
        if not self.current_project:
            logger.error("没有当前项目")
            return False

        try:
            shots = self.current_project["shots"]
            modified = False

            for shot_id, shot in shots.items():
                voice_duration = shot["voice"]["duration"]
                voice_text = shot["voice"]["text"]

                if voice_duration > max_duration and voice_text:
                    # 简单的文本分割逻辑（可以根据需要改进）
                    sentences = voice_text.split('。')
                    if len(sentences) > 1:
                        # 分割成多个镜头
                        for i, sentence in enumerate(sentences):
                            if sentence.strip():
                                new_shot_id = f"{shot_id}_{i+1}"
                                new_shot = shot.copy()
                                new_shot["id"] = new_shot_id
                                new_shot["title"] = f"{shot['title']}_{i+1}"
                                new_shot["voice"]["text"] = sentence.strip() + '。'
                                new_shot["voice"]["duration"] = voice_duration / len(sentences)
                                new_shot["voice"]["audio_path"] = None
                                new_shot["voice"]["status"] = "pending"

                                shots[new_shot_id] = new_shot

                        # 删除原镜头
                        del shots[shot_id]
                        modified = True

                        logger.info(f"长语音段已分割: {shot_id} -> {len(sentences)} 段")

            if modified:
                self._update_progress()
                logger.info("语音段分割完成")

            return True

        except Exception as e:
            logger.error(f"分割语音段失败: {e}")
            return False
