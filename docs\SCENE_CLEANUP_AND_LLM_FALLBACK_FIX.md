# 场景清除和LLM故障转移修复总结

## 🎯 问题分析

根据用户提供的日志，发现了两个关键问题：

### 1. 场景清除问题
**现象**：
```
[2025-06-26 09:20:35] [INFO] 已清除 2 个自动提取的角色和 0 个自动提取的场景
```
但实际上应该清除之前的自动提取场景。

**根本原因**：
- 场景清除逻辑只匹配以"镜头场景_"开头的场景ID
- 新版本自动提取的场景ID格式为"auto_场景名_时间戳"
- 清除逻辑没有包含"auto_"前缀的场景

### 2. LLM超时故障转移问题
**现象**：
```
[2025-06-26 09:19:06] [ERROR] LLM调用超时 (60秒)
[2025-06-26 09:19:09] [ERROR] LLM API请求失败
```
当前LLM超时后没有自动切换到其他可用的LLM模型。

**根本原因**：
- 缺少LLM服务的故障转移机制
- 单一LLM提供商失败时没有备选方案

## ✅ 修复方案

### 1. 场景清除功能修复

#### 更新清除逻辑
**文件**: `src/utils/character_scene_manager.py`

**修改前**：
```python
# 清除自动提取的场景（ID以"镜头场景_"开头的）
auto_scene_ids = [scene_id for scene_id in scenes.keys() if scene_id.startswith("镜头场景_")]
```

**修改后**：
```python
# 清除自动提取的场景（ID以"auto_"或"镜头场景_"开头的）
auto_scene_ids = [scene_id for scene_id in scenes.keys() 
                if scene_id.startswith("auto_") or scene_id.startswith("镜头场景_")]
```

#### 确保数据正确保存
**问题**: 删除操作后没有正确更新数据库文件

**修复**:
```python
# 角色清除
if auto_character_ids:
    characters_db["characters"] = characters  # 确保更新的字典被保存
    characters_db["last_updated"] = self._get_current_time()
    self._save_json(self.characters_file, characters_db)

# 场景清除  
if auto_scene_ids:
    scenes_db["scenes"] = scenes  # 确保更新的字典被保存
    scenes_db["last_updated"] = self._get_current_time()
    self._save_json(self.scenes_file, scenes_db)
```

### 2. LLM故障转移机制

#### 新增故障转移方法
**文件**: `src/services/llm_service.py`

**新增方法**:
```python
async def execute_with_fallback(self, prompt: str, max_tokens: int = 2000, 
                              temperature: float = 0.7, 
                              preferred_provider: Optional[str] = None) -> ServiceResult:
    """执行请求，支持自动故障转移"""
    # 定义提供商优先级顺序
    provider_priority = []
    
    # 如果指定了首选提供商，优先使用
    if preferred_provider:
        provider_priority.append(preferred_provider)
    
    # 添加其他可用提供商作为备选
    all_providers = ['zhipu', 'deepseek', 'tongyi', 'google']
    for provider in all_providers:
        if provider != preferred_provider:
            provider_priority.append(provider)
    
    last_error = None
    
    # 按优先级尝试每个提供商
    for provider in provider_priority:
        try:
            logger.info(f"尝试使用 {provider} 提供商...")
            result = await self.execute(
                provider=provider,
                prompt=prompt,
                max_tokens=max_tokens,
                temperature=temperature
            )
            
            if result.success:
                if provider != preferred_provider:
                    logger.info(f"故障转移成功：从 {preferred_provider} 切换到 {provider}")
                return result
            else:
                last_error = result.error
                logger.warning(f"提供商 {provider} 返回失败结果: {result.error}")
                
        except Exception as e:
            last_error = str(e)
            logger.warning(f"提供商 {provider} 调用失败: {e}")
            continue
    
    # 所有提供商都失败了
    error_msg = f"所有LLM提供商都不可用，最后错误: {last_error}"
    logger.error(error_msg)
    return ServiceResult(success=False, error=error_msg)
```

#### 集成到角色场景管理器
**文件**: `src/utils/character_scene_manager.py`

**新增方法**:
```python
def _execute_llm_with_fallback(self, llm_service, prompt: str, max_tokens: int = 3000,
                              temperature: float = 0.3, timeout: int = 60):
    """在线程池中执行LLM调用，支持故障转移"""
    import concurrent.futures
    import threading

    def run_async_in_thread():
        """在新线程中运行异步操作，支持故障转移"""
        try:
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                # 使用故障转移机制执行异步操作
                result = loop.run_until_complete(
                    llm_service.execute_with_fallback(
                        prompt=prompt, 
                        max_tokens=max_tokens, 
                        temperature=temperature
                    )
                )
                return result
            finally:
                # 清理事件循环
                # ... (清理代码)
        except Exception as e:
            logger.error(f"线程中执行LLM调用失败: {e}")
            return None

    # 使用线程池执行，设置超时
    with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
        future = executor.submit(run_async_in_thread)
        try:
            result = future.result(timeout=timeout)
            return result
        except concurrent.futures.TimeoutError:
            logger.error(f"LLM调用超时 ({timeout}秒)")
            return None
        except Exception as e:
            logger.error(f"LLM调用执行失败: {e}")
            return None
```

**更新调用逻辑**:
```python
# 角色提取
result = self._execute_llm_with_fallback(
    llm_service, prompt, max_tokens=3000, temperature=0.3, timeout=60
)

# 场景提取
result = self._execute_llm_with_fallback(
    llm_service, prompt, max_tokens=2000, temperature=0.3, timeout=60
)
```

## 🧪 测试验证

### 测试脚本
创建了 `scripts/test_scene_cleanup_and_llm_fallback.py` 进行全面测试。

### 测试结果
```
============================================================
📋 测试结果总结
============================================================
场景清除功能: ✅ 通过
LLM故障转移功能: ✅ 通过
角色场景管理器集成: ✅ 通过

总计: 3/3 个测试通过
🎉 所有测试通过! 场景清除和LLM故障转移功能正常工作
```

### 场景清除测试结果
```
📊 清除结果:
清除后场景数量: 2

✅ 保留的场景 (2个):
  - user_scene_1: 用户创建的场景1
  - user_scene_2: 用户创建的场景2

🗑️ 清除的场景 (3个):
  - auto_云溪村_2025-06-26 09_20_35: 云溪村
  - auto_阿明家_2025-06-26 09_20_35: 阿明家
  - 镜头场景_测试场景: 镜头场景

🎉 场景清除功能测试通过!
✅ 正确清除了以'auto_'和'镜头场景_'开头的场景
✅ 正确保留了用户创建的场景
```

## 🎯 修复效果

### 1. 场景清除功能
- ✅ **正确清除自动提取场景**: 现在能正确清除以"auto_"和"镜头场景_"开头的场景
- ✅ **保留用户创建场景**: 用户手动创建的场景不会被误删
- ✅ **数据一致性**: 确保删除操作正确保存到数据库文件

### 2. LLM故障转移功能
- ✅ **自动故障转移**: 当首选LLM提供商失败时，自动尝试其他可用提供商
- ✅ **提供商优先级**: 支持指定首选提供商，其他提供商作为备选
- ✅ **错误处理**: 完善的错误处理和日志记录
- ✅ **超时保护**: 保持原有的超时机制

### 3. 集成效果
- ✅ **无缝集成**: 故障转移功能已集成到角色场景管理器
- ✅ **向下兼容**: 保持与现有代码的兼容性
- ✅ **性能优化**: 不影响正常情况下的性能

## 🚀 使用效果

### 场景清除
现在当用户进行新的世界观分析时，系统会：
1. 正确清除所有以"auto_"开头的自动提取场景
2. 正确清除所有以"镜头场景_"开头的旧版场景
3. 保留所有用户手动创建的场景
4. 在日志中显示正确的清除数量

### LLM故障转移
现在当LLM调用失败时，系统会：
1. 自动尝试其他可用的LLM提供商
2. 在日志中显示故障转移过程
3. 只有在所有提供商都失败时才返回错误
4. 提高角色和场景提取的成功率

## 📝 后续建议

### 短期改进
1. **监控指标**: 添加故障转移成功率的监控
2. **用户通知**: 在UI中显示故障转移状态
3. **配置选项**: 允许用户自定义提供商优先级

### 长期规划
1. **智能选择**: 基于历史成功率动态调整提供商优先级
2. **负载均衡**: 在多个可用提供商之间分配负载
3. **缓存机制**: 缓存成功的提供商选择以提高效率

---
*修复完成时间: 2025-06-26*
*修复状态: ✅ 已完成并测试通过*
*影响范围: 角色场景管理、LLM服务调用*
