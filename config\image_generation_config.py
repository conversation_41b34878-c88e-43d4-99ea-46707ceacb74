# 图像生成配置文件

def get_config(environment=None):
    """获取图像生成配置"""
    return {
        "default_engine": "cogview_3_flash",  # 默认使用CogView-3-Flash
        "concurrent_limit": 5,  # 全局并发限制
        "routing_strategy": "free_first",  # 路由策略：优先使用免费引擎
        "output_dir": "output/images",
        "engines": {
            "pollinations": {
                "enabled": True,
                "name": "Pollinations AI",
                "description": "免费AI图像生成服务",
                "base_url": "https://image.pollinations.ai/prompt",
                "max_concurrent": 5,
                "timeout": 60,
                "max_retries": 3
            },
            "cogview_3_flash": {
                "enabled": True,
                "name": "CogView-3-Flash",
                "description": "智谱AI免费图像生成服务",
                "api_key": "ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY",
                "base_url": "https://open.bigmodel.cn/api/paas/v4",
                "model": "cogview-3-flash",
                "max_concurrent": 5,
                "timeout": 300,
                "max_retries": 3,
                "concurrent_limit": 5,
                "supported_sizes": [
                    "1024x1024", "1280x720", "720x1280", "1440x720", "720x1440",
                    "1024x768", "768x1024", "1152x896", "896x1152", "1216x832", "832x1216"
                ],
                "default_size": "1024x1024",
                "max_batch_size": 4,  # 每次最多生成4张图
                "cost_per_image": 0.0  # 免费
            },
            "comfyui_local": {
                "enabled": False,
                "name": "ComfyUI 本地",
                "description": "本地ComfyUI服务",
                "base_url": "http://127.0.0.1:8188",
                "max_concurrent": 3,
                "timeout": 120,
                "max_retries": 2
            }
        },
        "engine_preferences": ["free", "quality"]  # 引擎偏好：优先免费，然后质量
    }

def get_enabled_engines():
    """获取启用的引擎列表"""
    config = get_config()
    enabled_engines = []
    
    for engine_id, engine_config in config["engines"].items():
        if engine_config.get("enabled", False):
            enabled_engines.append({
                "id": engine_id,
                "name": engine_config["name"],
                "description": engine_config["description"]
            })
    
    return enabled_engines
