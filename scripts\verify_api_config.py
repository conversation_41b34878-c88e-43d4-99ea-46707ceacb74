#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API配置验证脚本
验证所有配置文件中的API密钥是否已正确设置
"""

import os
import json
import sys
from pathlib import Path

def load_json_config(file_path):
    """加载JSON配置文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 无法加载配置文件 {file_path}: {e}")
        return None

def load_python_config(file_path):
    """加载Python配置文件"""
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("config", file_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return module.get_config()
    except Exception as e:
        print(f"❌ 无法加载配置文件 {file_path}: {e}")
        return None

def verify_llm_config():
    """验证LLM配置"""
    print("\n🔍 验证LLM配置...")
    config_file = Path("config/llm_config.json")
    
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    config = load_json_config(config_file)
    if not config:
        return False
    
    expected_apis = {
        "智谱AI": "ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY",
        "通义千问": "sk-ab30df729a9b4df287db20a8f47ba12c", 
        "Deepseek": "***********************************",
        "Google Gemini": "AIzaSyA3Nh4nxQYoaZRxlJWUpvvVR_kU-ihITok"
    }
    
    all_correct = True
    for model in config.get("models", []):
        name = model.get("name")
        key = model.get("key")
        
        if name in expected_apis:
            if key == expected_apis[name]:
                print(f"✅ {name}: API密钥正确")
            else:
                print(f"❌ {name}: API密钥不匹配")
                print(f"   期望: {expected_apis[name]}")
                print(f"   实际: {key}")
                all_correct = False
        else:
            print(f"⚠️ {name}: 未知的API服务")
    
    return all_correct

def verify_image_config():
    """验证图像生成配置"""
    print("\n🔍 验证图像生成配置...")
    config_file = Path("config/image_generation_config.py")
    
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    config = load_python_config(config_file)
    if not config:
        return False
    
    cogview_config = config.get("engines", {}).get("cogview_3_flash", {})
    expected_key = "ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY"
    actual_key = cogview_config.get("api_key")
    
    if actual_key == expected_key:
        print("✅ CogView-3-Flash: API密钥正确")
        return True
    else:
        print("❌ CogView-3-Flash: API密钥不匹配")
        print(f"   期望: {expected_key}")
        print(f"   实际: {actual_key}")
        return False

def verify_video_config():
    """验证视频生成配置"""
    print("\n🔍 验证视频生成配置...")
    config_file = Path("config/video_generation_config.py")
    
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    config = load_python_config(config_file)
    if not config:
        return False
    
    cogvideox_config = config.get("engines", {}).get("cogvideox_flash", {})
    expected_key = "ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY"
    actual_key = cogvideox_config.get("api_key")
    
    if actual_key == expected_key:
        print("✅ CogVideoX-Flash: API密钥正确")
        return True
    else:
        print("❌ CogVideoX-Flash: API密钥不匹配")
        print(f"   期望: {expected_key}")
        print(f"   实际: {actual_key}")
        return False

def main():
    """主函数"""
    print("🔧 API配置验证工具")
    print("=" * 50)
    
    # 切换到项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    os.chdir(project_root)
    
    results = []
    results.append(verify_llm_config())
    results.append(verify_image_config())
    results.append(verify_video_config())
    
    print("\n" + "=" * 50)
    if all(results):
        print("🎉 所有API配置验证通过！")
        return 0
    else:
        print("❌ 部分API配置存在问题，请检查上述错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())
