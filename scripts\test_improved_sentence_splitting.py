#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进的智能断句算法
验证语义感知断句和时长平衡功能
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_improved_sentence_splitting():
    """测试改进的智能断句算法"""
    print("🔧 测试改进的智能断句算法")
    print("=" * 60)
    
    # 模拟改进的断句算法
    class ImprovedSentenceSplitter:
        def _estimate_speech_duration(self, text):
            return len(text) / 4.5
        
        def _calculate_semantic_score(self, text):
            if not text:
                return 0
            
            score = 0
            if text[-1] in '。！？；：，':
                score += 10
            elif text[-1] in '、 \t':
                score += 5
            
            bad_endings = ['的', '了', '着', '过', '在', '是', '有', '个', '一', '这', '那']
            if text[-1] in bad_endings:
                score -= 5
            
            if '，' in text and not text.endswith('，'):
                score += 3
            
            return score
        
        def _calculate_length_score(self, length, target, min_len, max_len):
            if length < min_len:
                return 0
            elif length > max_len:
                return max(0, 10 - (length - max_len) * 2)
            else:
                distance = abs(length - target)
                return max(0, 10 - distance)
        
        def _smart_split_sentence(self, sentence):
            min_length = 18
            target_length = 20
            max_length = 22
            
            if len(sentence) <= max_length:
                return [sentence]
            
            semantic_breaks = [
                ('。', 1, True), ('！', 1, True), ('？', 1, True),
                ('；', 2, True), ('：', 2, True), ('，', 3, True),
                ('、', 4, True), (' ', 5, False), ('\t', 5, False),
                ('和', 6, True), ('与', 6, True), ('及', 6, True), ('或', 6, True),
                ('的', 8, False), ('了', 8, False), ('着', 8, False), ('过', 8, False),
            ]
            
            result = []
            remaining = sentence
            
            while len(remaining) > max_length:
                best_candidates = []
                
                search_start = min_length
                search_end = min(len(remaining), max_length + 5)
                
                for i in range(search_start, search_end):
                    if i >= len(remaining):
                        break
                        
                    char = remaining[i]
                    for break_char, priority, include_char in semantic_breaks:
                        if char == break_char:
                            pos = i + 1 if include_char else i
                            length = pos
                            
                            semantic_score = self._calculate_semantic_score(remaining[:pos])
                            length_score = self._calculate_length_score(length, target_length, min_length, max_length)
                            total_score = semantic_score * 0.7 + length_score * 0.3 - priority * 0.1
                            
                            best_candidates.append({
                                'pos': pos,
                                'score': total_score,
                                'length': length,
                                'char': break_char,
                                'priority': priority,
                                'semantic_score': semantic_score,
                                'length_score': length_score
                            })
                
                if best_candidates:
                    best_candidates.sort(key=lambda x: x['score'], reverse=True)
                    best = best_candidates[0]
                    
                    if best['length'] <= max_length + 2:
                        part = remaining[:best['pos']].strip()
                        if part:
                            result.append(part)
                        remaining = remaining[best['pos']:].strip()
                    else:
                        suitable = [c for c in best_candidates if c['length'] <= max_length]
                        if suitable:
                            best = suitable[0]
                            part = remaining[:best['pos']].strip()
                            if part:
                                result.append(part)
                            remaining = remaining[best['pos']:].strip()
                        else:
                            part = remaining[:target_length].strip()
                            if part:
                                result.append(part)
                            remaining = remaining[target_length:].strip()
                else:
                    part = remaining[:target_length].strip()
                    if part:
                        result.append(part)
                    remaining = remaining[target_length:].strip()
            
            if remaining.strip():
                last_part = remaining.strip()
                if len(last_part) < min_length and result:
                    combined = result[-1] + last_part
                    if len(combined) <= max_length + 3:
                        result[-1] = combined
                    else:
                        result.append(last_part)
                else:
                    result.append(last_part)
            
            return result
        
        def _balance_sentence_lengths(self, sentences):
            if len(sentences) <= 1:
                return sentences
            
            balanced = []
            i = 0
            
            while i < len(sentences):
                current = sentences[i]
                current_duration = self._estimate_speech_duration(current)
                
                if current_duration < 3.0 and i + 1 < len(sentences):
                    next_sentence = sentences[i + 1]
                    combined = current + next_sentence
                    combined_duration = self._estimate_speech_duration(combined)
                    
                    if combined_duration <= 5.0:
                        balanced.append(combined)
                        i += 2
                        continue
                
                if current_duration > 5.0:
                    further_split = self._smart_split_sentence(current)
                    balanced.extend(further_split)
                else:
                    balanced.append(current)
                
                i += 1
            
            return balanced
        
        def optimize_sentences(self, sentences):
            optimized = []
            
            for sentence in sentences:
                duration = self._estimate_speech_duration(sentence)
                
                if duration <= 5.0:
                    optimized.append(sentence)
                else:
                    split_sentences = self._smart_split_sentence(sentence)
                    balanced_sentences = self._balance_sentence_lengths(split_sentences)
                    optimized.extend(balanced_sentences)
            
            return optimized
    
    splitter = ImprovedSentenceSplitter()
    
    # 测试用例 - 特别针对您提到的问题
    test_cases = [
        {
            "name": "月球基地案例（原问题）",
            "text": "在月球基地的控制室里，宇航员张伟正在检查各项设备的运行状态，突然警报声响起。",
            "expected_issues": ["避免硬性断句", "避免时长过短"]
        },
        {
            "name": "长句子自然断句",
            "text": "科学家们经过多年的研究和实验，终于在实验室中成功合成了这种新型材料，这一突破将对未来的科技发展产生深远影响。",
            "expected_issues": ["保持语义完整", "时长平衡"]
        },
        {
            "name": "多逗号复杂句",
            "text": "在古代战场上，将军们指挥着千军万马，战鼓声震天动地，士兵们奋勇杀敌，场面极其壮观。",
            "expected_issues": ["自然断句点", "避免过短片段"]
        }
    ]
    
    print("📝 测试改进的断句算法：")
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{'='*50}")
        print(f"测试案例 {i}：{case['name']}")
        print(f"{'='*50}")
        
        original = case['text']
        print(f"原句：{original}")
        print(f"长度：{len(original)}字符")
        print(f"预估时长：{splitter._estimate_speech_duration(original):.1f}秒")
        
        # 使用改进算法拆分
        result = splitter.optimize_sentences([original])
        
        print(f"\n🎬 改进算法结果：")
        total_duration = 0
        all_within_limit = True
        min_duration = float('inf')
        max_duration = 0
        
        for j, part in enumerate(result, 1):
            duration = splitter._estimate_speech_duration(part)
            total_duration += duration
            min_duration = min(min_duration, duration)
            max_duration = max(max_duration, duration)
            
            status = "✅" if duration <= 5.0 else "❌"
            if duration > 5.0:
                all_within_limit = False
            
            # 检查语义完整性
            semantic_score = splitter._calculate_semantic_score(part)
            semantic_status = "🟢" if semantic_score >= 5 else "🟡" if semantic_score >= 0 else "🔴"
            
            print(f"  片段{j}：{part}")
            print(f"    长度：{len(part)}字符 | 时长：{duration:.1f}秒 {status}")
            print(f"    语义得分：{semantic_score} {semantic_status}")
        
        print(f"\n📊 统计结果：")
        print(f"  片段数量：{len(result)}")
        print(f"  总时长：{total_duration:.1f}秒")
        print(f"  平均时长：{total_duration/len(result):.1f}秒")
        print(f"  最短时长：{min_duration:.1f}秒")
        print(f"  最长时长：{max_duration:.1f}秒")
        print(f"  时长达标率：{sum(1 for r in result if splitter._estimate_speech_duration(r) <= 5.0)/len(result)*100:.1f}%")
        
        # 评估改进效果
        print(f"\n🎯 改进效果评估：")
        if all_within_limit:
            print("  ✅ 所有片段都在5秒限制内")
        else:
            over_limit = sum(1 for r in result if splitter._estimate_speech_duration(r) > 5.0)
            print(f"  ⚠️ {over_limit}个片段超过5秒限制")
        
        if min_duration >= 3.0:
            print("  ✅ 避免了过短片段（≥3秒）")
        else:
            short_count = sum(1 for r in result if splitter._estimate_speech_duration(r) < 3.0)
            print(f"  ⚠️ {short_count}个片段过短（<3秒）")
        
        # 检查语义完整性
        good_semantic = sum(1 for r in result if splitter._calculate_semantic_score(r) >= 5)
        print(f"  📝 语义完整性：{good_semantic}/{len(result)}个片段良好")
    
    print(f"\n{'='*60}")
    print("🎉 改进的智能断句算法测试完成！")
    
    return True

def main():
    """主函数"""
    print("🚀 开始测试改进的智能断句算法")
    
    os.chdir(project_root)
    
    success = test_improved_sentence_splitting()
    
    if success:
        print("\n🎉 测试通过！改进的断句算法有效解决了硬性断句和时长过短问题")
        return 0
    else:
        print("\n❌ 测试失败，需要进一步优化")
        return 1

if __name__ == "__main__":
    sys.exit(main())
