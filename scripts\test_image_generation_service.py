#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图像生成服务初始化
验证CogView-3-Flash引擎是否正确初始化并支持并发和批量生成
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_image_generation_service():
    """测试图像生成服务"""
    print("🔧 测试图像生成服务初始化")
    print("=" * 50)
    
    try:
        from src.models.image_generation_service import ImageGenerationService
        from config.image_generation_config import get_config
        
        # 获取配置
        config = get_config()
        print(f"✅ 配置加载成功")
        print(f"   默认引擎: {config.get('default_engine')}")
        print(f"   并发限制: {config.get('concurrent_limit')}")
        
        # 检查CogView-3-Flash配置
        cogview_config = config.get('engines', {}).get('cogview_3_flash', {})
        if cogview_config:
            print(f"✅ CogView-3-Flash配置:")
            print(f"   启用状态: {cogview_config.get('enabled')}")
            print(f"   API密钥: {cogview_config.get('api_key', '')[:10]}...")
            print(f"   最大并发: {cogview_config.get('max_concurrent')}")
            print(f"   最大批量: {cogview_config.get('max_batch_size')}")
        else:
            print("❌ CogView-3-Flash配置未找到")
            return False
        
        # 创建服务实例
        print("\n🔍 创建图像生成服务实例...")
        service = ImageGenerationService(config)
        print("✅ 服务实例创建成功")
        
        # 初始化服务
        print("\n🔍 初始化图像生成服务...")
        success = await service.initialize()
        
        if success:
            print("✅ 图像生成服务初始化成功")
        else:
            print("❌ 图像生成服务初始化失败")
            return False
        
        # 测试连接
        print("\n🔍 测试引擎连接...")
        connections = await service.test_connection()
        
        for engine_name, status in connections.items():
            if status:
                print(f"✅ {engine_name}: 连接成功")
            else:
                print(f"❌ {engine_name}: 连接失败")
        
        # 检查引擎管理器状态
        print("\n🔍 检查引擎管理器状态...")
        if hasattr(service, 'engine_manager'):
            manager = service.engine_manager
            print(f"✅ 引擎管理器存在")
            print(f"   并发限制: {getattr(manager, 'concurrent_limit', 'N/A')}")
            print(f"   可用引擎数: {len(getattr(manager, 'engines', {}))}")
            
            # 列出可用引擎
            if hasattr(manager, 'engines'):
                for engine_type, engine in manager.engines.items():
                    print(f"   - {engine_type.value}: {engine.status.value}")
        else:
            print("❌ 引擎管理器不存在")
            return False
        
        print("\n🎉 所有测试通过！图像生成服务配置正确")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_cogview_engine_directly():
    """直接测试CogView-3-Flash引擎"""
    print("\n" + "=" * 50)
    print("🔧 直接测试CogView-3-Flash引擎")
    print("=" * 50)
    
    try:
        from src.models.engines.cogview3_flash_engine import CogView3FlashEngine
        from config.image_generation_config import get_config
        
        # 获取配置
        config = get_config()
        cogview_config = config.get('engines', {}).get('cogview_3_flash', {})
        
        # 创建引擎实例
        print("🔍 创建CogView-3-Flash引擎实例...")
        engine = CogView3FlashEngine(cogview_config)
        print("✅ 引擎实例创建成功")
        
        # 初始化引擎
        print("\n🔍 初始化引擎...")
        success = await engine.initialize()
        
        if success:
            print("✅ 引擎初始化成功")
            print(f"   状态: {engine.status.value}")
            print(f"   并发限制: {engine.concurrent_limit}")
        else:
            print("❌ 引擎初始化失败")
            print(f"   错误: {engine.last_error}")
            return False
        
        # 测试连接
        print("\n🔍 测试引擎连接...")
        connection_ok = await engine.test_connection()
        
        if connection_ok:
            print("✅ 引擎连接测试成功")
        else:
            print("❌ 引擎连接测试失败")
            return False
        
        # 获取引擎信息
        print("\n🔍 获取引擎信息...")
        info = engine.get_engine_info()
        print(f"✅ 引擎信息:")
        print(f"   名称: {info.name}")
        print(f"   版本: {info.version}")
        print(f"   免费: {info.is_free}")
        print(f"   支持批量: {info.supports_batch}")
        print(f"   最大批量: {info.max_batch_size}")
        print(f"   速率限制: {info.rate_limit}")
        
        # 清理资源
        await engine.cleanup()
        print("✅ 引擎资源清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接测试引擎失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 开始测试图像生成服务和引擎")
    
    # 切换到项目根目录
    os.chdir(project_root)
    
    # 测试服务
    service_ok = await test_image_generation_service()
    
    # 直接测试引擎
    engine_ok = await test_cogview_engine_directly()
    
    print("\n" + "=" * 50)
    if service_ok and engine_ok:
        print("🎉 所有测试通过！图像生成功能应该正常工作")
        return 0
    else:
        print("❌ 部分测试失败，请检查配置和网络连接")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
