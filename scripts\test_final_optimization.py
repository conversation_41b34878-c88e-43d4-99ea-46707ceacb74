#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终优化验证测试
验证改进后的5秒视频限制优化是否完全解决了硬性断句和时长问题
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_final_optimization():
    """测试最终优化效果"""
    print("🔧 最终优化验证测试")
    print("=" * 60)
    
    # 使用最新的参数设置
    class FinalOptimizedSplitter:
        def _estimate_speech_duration(self, text):
            # 更保守的估算：4.2字符/秒
            return len(text) / 4.2
        
        def _calculate_semantic_score(self, text):
            if not text:
                return 0
            
            score = 0
            if text[-1] in '。！？；：，':
                score += 10
            elif text[-1] in '、 \t':
                score += 5
            
            bad_endings = ['的', '了', '着', '过', '在', '是', '有', '个', '一', '这', '那']
            if text[-1] in bad_endings:
                score -= 5
            
            if '，' in text and not text.endswith('，'):
                score += 3
            
            return score
        
        def _calculate_length_score(self, length, target, min_len, max_len):
            if length < min_len:
                return 0
            elif length > max_len:
                return max(0, 10 - (length - max_len) * 2)
            else:
                distance = abs(length - target)
                return max(0, 10 - distance)
        
        def _smart_split_sentence(self, sentence):
            # 更严格的参数：16-21字符，确保≤5秒
            min_length = 16
            target_length = 19
            max_length = 21
            
            if len(sentence) <= max_length:
                return [sentence]
            
            semantic_breaks = [
                ('。', 1, True), ('！', 1, True), ('？', 1, True),
                ('；', 2, True), ('：', 2, True), ('，', 3, True),
                ('、', 4, True), (' ', 5, False), ('\t', 5, False),
                ('和', 6, True), ('与', 6, True), ('及', 6, True), ('或', 6, True),
                ('的', 8, False), ('了', 8, False), ('着', 8, False), ('过', 8, False),
            ]
            
            result = []
            remaining = sentence
            
            while len(remaining) > max_length:
                best_candidates = []
                
                search_start = min_length
                search_end = min(len(remaining), max_length + 3)  # 稍微扩大搜索范围
                
                for i in range(search_start, search_end):
                    if i >= len(remaining):
                        break
                        
                    char = remaining[i]
                    for break_char, priority, include_char in semantic_breaks:
                        if char == break_char:
                            pos = i + 1 if include_char else i
                            length = pos
                            
                            semantic_score = self._calculate_semantic_score(remaining[:pos])
                            length_score = self._calculate_length_score(length, target_length, min_length, max_length)
                            total_score = semantic_score * 0.7 + length_score * 0.3 - priority * 0.1
                            
                            best_candidates.append({
                                'pos': pos,
                                'score': total_score,
                                'length': length,
                                'char': break_char,
                                'semantic_score': semantic_score,
                                'length_score': length_score
                            })
                
                if best_candidates:
                    # 优先选择长度在限制内的候选
                    valid_candidates = [c for c in best_candidates if c['length'] <= max_length]
                    if valid_candidates:
                        valid_candidates.sort(key=lambda x: x['score'], reverse=True)
                        best = valid_candidates[0]
                    else:
                        # 如果没有有效候选，选择最短的
                        best_candidates.sort(key=lambda x: x['length'])
                        best = best_candidates[0]
                    
                    part = remaining[:best['pos']].strip()
                    if part:
                        result.append(part)
                    remaining = remaining[best['pos']:].strip()
                else:
                    # 强制在目标位置断句
                    part = remaining[:target_length].strip()
                    if part:
                        result.append(part)
                    remaining = remaining[target_length:].strip()
            
            if remaining.strip():
                last_part = remaining.strip()
                if len(last_part) < min_length and result:
                    combined = result[-1] + last_part
                    if len(combined) <= max_length:
                        result[-1] = combined
                    else:
                        result.append(last_part)
                else:
                    result.append(last_part)
            
            return result
        
        def optimize_sentences(self, sentences):
            optimized = []
            
            for sentence in sentences:
                duration = self._estimate_speech_duration(sentence)
                
                if duration <= 5.0:
                    optimized.append(sentence)
                else:
                    split_sentences = self._smart_split_sentence(sentence)
                    # 再次检查每个片段
                    final_sentences = []
                    for part in split_sentences:
                        part_duration = self._estimate_speech_duration(part)
                        if part_duration <= 5.0:
                            final_sentences.append(part)
                        else:
                            # 如果还是超时，进一步拆分
                            further_split = self._smart_split_sentence(part)
                            final_sentences.extend(further_split)
                    optimized.extend(final_sentences)
            
            return optimized
    
    splitter = FinalOptimizedSplitter()
    
    # 重点测试原问题案例
    test_cases = [
        {
            "name": "月球基地案例（原问题重测）",
            "text": "在月球基地的控制室里，宇航员张伟正在检查各项设备的运行状态，突然警报声响起。"
        },
        {
            "name": "复杂长句测试",
            "text": "科学家们经过多年的研究和实验，终于在实验室中成功合成了这种新型材料，这一突破将对未来的科技发展产生深远影响。"
        },
        {
            "name": "多逗号句子测试",
            "text": "在古代战场上，将军们指挥着千军万马，战鼓声震天动地，士兵们奋勇杀敌，场面极其壮观。"
        }
    ]
    
    print("📝 最终优化效果验证：")
    
    all_passed = True
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{'='*50}")
        print(f"测试案例 {i}：{case['name']}")
        print(f"{'='*50}")
        
        original = case['text']
        print(f"原句：{original}")
        print(f"长度：{len(original)}字符")
        print(f"预估时长：{splitter._estimate_speech_duration(original):.1f}秒")
        
        result = splitter.optimize_sentences([original])
        
        print(f"\n🎬 最终优化结果：")
        
        case_passed = True
        min_duration = float('inf')
        max_duration = 0
        
        for j, part in enumerate(result, 1):
            duration = splitter._estimate_speech_duration(part)
            min_duration = min(min_duration, duration)
            max_duration = max(max_duration, duration)
            
            # 严格检查5秒限制
            time_status = "✅" if duration <= 5.0 else "❌"
            if duration > 5.0:
                case_passed = False
            
            # 检查是否过短
            short_status = "⚠️" if duration < 3.0 else "✅"
            if duration < 3.0:
                print(f"    注意：片段{j}时长较短")
            
            # 语义完整性
            semantic_score = splitter._calculate_semantic_score(part)
            semantic_status = "🟢" if semantic_score >= 5 else "🟡" if semantic_score >= 0 else "🔴"
            
            print(f"  片段{j}：{part}")
            print(f"    长度：{len(part)}字符 | 时长：{duration:.1f}秒 {time_status}")
            print(f"    语义得分：{semantic_score} {semantic_status}")
        
        print(f"\n📊 案例统计：")
        print(f"  片段数量：{len(result)}")
        print(f"  时长范围：{min_duration:.1f}s - {max_duration:.1f}s")
        print(f"  平均时长：{sum(splitter._estimate_speech_duration(r) for r in result)/len(result):.1f}秒")
        
        # 严格验证
        over_limit = sum(1 for r in result if splitter._estimate_speech_duration(r) > 5.0)
        under_3s = sum(1 for r in result if splitter._estimate_speech_duration(r) < 3.0)
        
        print(f"\n🎯 验证结果：")
        if over_limit == 0:
            print("  ✅ 所有片段都在5秒限制内")
        else:
            print(f"  ❌ {over_limit}个片段超过5秒限制")
            case_passed = False
        
        if under_3s == 0:
            print("  ✅ 没有过短片段")
        else:
            print(f"  ⚠️ {under_3s}个片段过短（<3秒）")
        
        # 检查语义断句质量
        bad_breaks = sum(1 for r in result if splitter._calculate_semantic_score(r) < 0)
        if bad_breaks == 0:
            print("  ✅ 语义断句质量良好")
        else:
            print(f"  ⚠️ {bad_breaks}个片段语义断句不佳")
        
        if case_passed:
            print("  🎉 案例测试通过！")
        else:
            print("  ❌ 案例测试失败")
            all_passed = False
    
    print(f"\n{'='*60}")
    print("📋 最终验证总结")
    print(f"{'='*60}")
    
    if all_passed:
        print("🎉 所有测试案例通过！")
        print("✅ 成功解决硬性断句问题")
        print("✅ 成功解决时长过短问题")
        print("✅ 严格遵守5秒时长限制")
        print("✅ 保持语义完整性")
        return True
    else:
        print("❌ 部分测试案例失败")
        print("需要进一步优化算法")
        return False

def main():
    """主函数"""
    print("🚀 开始最终优化验证测试")
    
    os.chdir(project_root)
    
    success = test_final_optimization()
    
    if success:
        print("\n🎊 最终优化验证通过！5秒视频限制优化已完美解决所有问题")
        return 0
    else:
        print("\n⚠️ 最终验证未完全通过，需要继续优化")
        return 1

if __name__ == "__main__":
    sys.exit(main())
