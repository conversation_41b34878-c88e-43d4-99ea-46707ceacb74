#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试引擎偏好设置修复
验证CogView-3-Flash引擎是否正确包含在偏好设置中
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_engine_preferences():
    """测试引擎偏好设置"""
    print("🔧 测试引擎偏好设置修复")
    print("=" * 50)
    
    try:
        from src.models.image_generation_service import ImageGenerationService
        from config.image_generation_config import get_config
        
        # 获取配置
        config = get_config()
        print(f"✅ 配置加载成功")
        print(f"   引擎偏好: {config.get('engine_preferences')}")
        
        # 创建服务实例
        print("\n🔍 创建图像生成服务实例...")
        service = ImageGenerationService(config)
        print("✅ 服务实例创建成功")
        
        # 初始化服务
        print("\n🔍 初始化图像生成服务...")
        success = await service.initialize()
        
        if success:
            print("✅ 图像生成服务初始化成功")
        else:
            print("❌ 图像生成服务初始化失败")
            return False
        
        # 检查引擎管理器的偏好设置
        print("\n🔍 检查引擎管理器偏好设置...")
        if hasattr(service, 'engine_manager'):
            manager = service.engine_manager
            preferences = manager.engine_preferences
            
            print(f"✅ 引擎偏好设置数量: {len(preferences)}")
            for i, pref in enumerate(preferences):
                print(f"   {i+1}. {pref.engine_type.value}: 优先级={pref.priority}, 启用={pref.enabled}, 最大成本={pref.max_cost_per_image}")
            
            # 检查是否包含CogView-3-Flash
            cogview_found = any(pref.engine_type.value == 'cogview_3_flash' for pref in preferences)
            if cogview_found:
                print("✅ CogView-3-Flash引擎已包含在偏好设置中")
            else:
                print("❌ CogView-3-Flash引擎未包含在偏好设置中")
                return False
        else:
            print("❌ 引擎管理器不存在")
            return False
        
        # 测试获取可用引擎
        print("\n🔍 测试获取可用引擎...")
        available_engines = manager._get_available_engines()
        print(f"✅ 可用引擎数量: {len(available_engines)}")
        
        for engine in available_engines:
            print(f"   - {engine.engine_type.value}: 状态={engine.status.value}")
        
        # 检查CogView-3-Flash是否可用
        cogview_available = any(engine.engine_type.value == 'cogview_3_flash' for engine in available_engines)
        if cogview_available:
            print("✅ CogView-3-Flash引擎在可用引擎列表中")
        else:
            print("❌ CogView-3-Flash引擎不在可用引擎列表中")
            
            # 检查引擎状态
            cogview_engine = manager.factory.get_engine(manager.factory.EngineType.COGVIEW_3_FLASH)
            if cogview_engine:
                print(f"   CogView-3-Flash引擎状态: {cogview_engine.status.value}")
            else:
                print("   CogView-3-Flash引擎未创建")
        
        # 测试引擎选择
        print("\n🔍 测试引擎选择...")
        from src.models.image_engine_base import GenerationConfig
        
        test_config = GenerationConfig(
            prompt="测试提示词",
            width=1024,
            height=1024,
            batch_size=1
        )
        
        selected_engine = await manager._select_best_engine(test_config)
        if selected_engine:
            print(f"✅ 成功选择引擎: {selected_engine.engine_type.value}")
        else:
            print("❌ 未能选择到可用引擎")
            return False
        
        print("\n🎉 所有测试通过！引擎偏好设置修复成功")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_direct_generation():
    """测试直接图像生成"""
    print("\n" + "=" * 50)
    print("🔧 测试直接图像生成")
    print("=" * 50)
    
    try:
        from src.models.image_generation_service import ImageGenerationService
        from config.image_generation_config import get_config
        
        # 获取配置
        config = get_config()
        
        # 创建服务实例
        service = ImageGenerationService(config)
        
        # 初始化服务
        await service.initialize()
        
        # 测试生成图像
        print("🔍 测试生成图像...")
        result = await service.generate_image(
            prompt="一只可爱的小猫",
            config={
                'width': 1024,
                'height': 1024,
                'batch_size': 1
            },
            engine_preference='cogview_3_flash'
        )
        
        if result.success:
            print("✅ 图像生成测试成功")
            print(f"   生成的图像路径: {result.image_paths}")
        else:
            print("❌ 图像生成测试失败")
            print(f"   错误信息: {result.error_message}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 直接生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 开始测试引擎偏好设置修复")
    
    # 切换到项目根目录
    os.chdir(project_root)
    
    # 测试偏好设置
    preferences_ok = await test_engine_preferences()
    
    # 测试直接生成（如果偏好设置正确）
    generation_ok = False
    if preferences_ok:
        generation_ok = await test_direct_generation()
    
    print("\n" + "=" * 50)
    if preferences_ok and generation_ok:
        print("🎉 所有测试通过！引擎偏好设置修复成功，图像生成功能正常")
        return 0
    elif preferences_ok:
        print("⚠️ 引擎偏好设置修复成功，但图像生成测试失败")
        return 1
    else:
        print("❌ 引擎偏好设置修复失败")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
