#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ProjectManagerAdapter修复
验证所有必需的方法是否已正确实现
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_project_manager_adapter():
    """测试ProjectManagerAdapter的方法"""
    print("🔧 测试ProjectManagerAdapter修复")
    print("=" * 50)
    
    try:
        from src.core.project_manager_adapter import ProjectManagerAdapter
        
        # 创建适配器实例
        config_dir = str(project_root / "config")
        adapter = ProjectManagerAdapter(config_dir)
        
        print("✅ ProjectManagerAdapter实例化成功")
        
        # 测试必需的方法是否存在
        required_methods = [
            'get_current_project_path',
            'get_project_root', 
            'get_project_path',
            'get_project_file_path',
            'get_project_config_path',
            'create_new_project',
            'load_project',
            'save_project',
            'get_project_data',
            'list_projects'
        ]
        
        missing_methods = []
        for method_name in required_methods:
            if hasattr(adapter, method_name):
                print(f"✅ 方法存在: {method_name}")
            else:
                missing_methods.append(method_name)
                print(f"❌ 方法缺失: {method_name}")
        
        if missing_methods:
            print(f"\n❌ 发现缺失方法: {missing_methods}")
            return False
        
        # 测试get_current_project_path方法
        print("\n🔍 测试get_current_project_path方法...")
        try:
            path = adapter.get_current_project_path()
            print(f"✅ get_current_project_path()返回: '{path}'")
        except Exception as e:
            print(f"❌ get_current_project_path()调用失败: {e}")
            return False
        
        # 测试get_project_root方法
        print("\n🔍 测试get_project_root方法...")
        try:
            path = adapter.get_project_root()
            print(f"✅ get_project_root()返回: '{path}'")
        except Exception as e:
            print(f"❌ get_project_root()调用失败: {e}")
            return False
        
        # 测试get_project_path方法
        print("\n🔍 测试get_project_path方法...")
        try:
            path = adapter.get_project_path("test_project")
            print(f"✅ get_project_path('test_project')返回: '{path}'")
        except Exception as e:
            print(f"❌ get_project_path()调用失败: {e}")
            return False
        
        print("\n🎉 所有测试通过！ProjectManagerAdapter修复成功")
        return True
        
    except ImportError as e:
        print(f"❌ 导入ProjectManagerAdapter失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    success = test_project_manager_adapter()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
