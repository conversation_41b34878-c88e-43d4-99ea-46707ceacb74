#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API状态和角色提取功能
验证智谱AI、DeepSeek等API的可用性，以及角色提取功能
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_llm_apis():
    """测试LLM API的可用性"""
    print("🔧 测试LLM API可用性")
    print("=" * 60)
    
    try:
        from src.core.api_manager import APIManager
        from src.services.llm_service import LLMService
        
        # 创建API管理器和LLM服务
        api_manager = APIManager()
        llm_service = LLMService(api_manager)
        
        # 测试提示词
        test_prompt = "请简单回答：你好，这是一个API连接测试。"
        
        # 测试各个提供商
        providers = ['zhipu', 'deepseek', 'tongyi', 'google']
        
        results = {}
        
        for provider in providers:
            print(f"\n📡 测试 {provider} API...")
            try:
                result = await llm_service.execute(
                    provider=provider,
                    prompt=test_prompt,
                    max_tokens=100,
                    temperature=0.3
                )
                
                if result.success:
                    content = result.data.get('content', '')[:100] + '...' if len(result.data.get('content', '')) > 100 else result.data.get('content', '')
                    print(f"✅ {provider}: 连接成功")
                    print(f"   响应: {content}")
                    results[provider] = True
                else:
                    print(f"❌ {provider}: 调用失败 - {result.error}")
                    results[provider] = False
                    
            except Exception as e:
                print(f"❌ {provider}: 连接异常 - {e}")
                results[provider] = False
        
        # 输出总结
        print(f"\n{'='*60}")
        print("📊 API测试结果总结")
        print(f"{'='*60}")
        
        working_count = sum(1 for status in results.values() if status)
        total_count = len(results)
        
        for provider, status in results.items():
            status_text = "✅ 可用" if status else "❌ 不可用"
            print(f"{provider:10}: {status_text}")
        
        print(f"\n总计: {working_count}/{total_count} 个API可用")
        
        return results
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return {}

def test_character_extraction():
    """测试角色提取功能"""
    print("\n🔧 测试角色提取功能")
    print("=" * 60)
    
    try:
        from src.utils.character_scene_manager import CharacterSceneManager
        from src.core.service_manager import ServiceManager
        
        # 创建服务管理器
        service_manager = ServiceManager()
        
        # 创建角色场景管理器
        manager = CharacterSceneManager(str(project_root), service_manager)
        
        # 测试文本
        test_text = """
        在云溪村，年轻的阿明正在小溪边洗衣服。突然，一位白发苍苍的诗仙从天而降，
        手持酒壶，醉眼朦胧地看着阿明。村长老王听到动静，急忙赶来查看情况。
        诗仙李白微笑着对阿明说："小伙子，你可愿意与我一同游历天下？"
        """
        
        # 世界观圣经
        world_bible = """
        这是一个古代中国的故事，发生在唐朝时期。主要角色包括：
        - 阿明：村中的年轻人，勤劳善良
        - 诗仙：神秘的仙人，实际上是李白
        """
        
        print("📝 测试文本:")
        print(test_text.strip())
        print(f"\n📖 世界观圣经:")
        print(world_bible.strip())
        
        print(f"\n🔍 开始角色提取...")
        
        # 执行角色提取
        characters = manager.extract_characters_from_text(test_text, world_bible)
        
        print(f"\n📊 提取结果:")
        print(f"提取到 {len(characters)} 个角色")
        
        if characters:
            for i, char in enumerate(characters, 1):
                print(f"\n角色 {i}:")
                print(f"  名称: {char.get('name', '未知')}")
                print(f"  描述: {char.get('description', '无描述')}")
                print(f"  类型: {char.get('type', '未知')}")
                print(f"  重要性: {char.get('importance', '未知')}")
        else:
            print("❌ 未提取到任何角色")
            
            # 分析可能的原因
            print(f"\n🔍 问题分析:")
            print("可能的原因:")
            print("1. LLM服务不可用或配置错误")
            print("2. 提示词格式问题")
            print("3. 响应解析失败")
            print("4. 网络连接问题")
        
        return len(characters) > 0
        
    except Exception as e:
        print(f"❌ 角色提取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_fallback_mechanism():
    """测试故障转移机制"""
    print("\n🔧 测试故障转移机制")
    print("=" * 60)

    try:
        from src.core.api_manager import APIManager
        from src.services.llm_service import LLMService

        # 创建API管理器和LLM服务
        api_manager = APIManager()
        llm_service = LLMService(api_manager)

        # 测试故障转移
        test_prompt = "请简单回答：故障转移测试。"

        print("📡 测试故障转移机制...")

        # 使用故障转移方法
        result = await llm_service.execute_with_fallback(
            prompt=test_prompt,
            max_tokens=100,
            temperature=0.3,
            preferred_provider='zhipu'  # 指定首选提供商
        )
        
        if result.success:
            print("✅ 故障转移机制工作正常")
            print(f"   响应: {result.data.get('content', '')[:100]}...")
            print(f"   使用的提供商: {result.metadata.get('provider', '未知')}")
            return True
        else:
            print(f"❌ 故障转移失败: {result.error}")
            return False
            
    except Exception as e:
        print(f"❌ 故障转移测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 开始API状态和角色提取功能测试")
    
    os.chdir(project_root)
    
    # 测试API可用性
    api_results = await test_llm_apis()
    
    # 测试故障转移机制
    fallback_result = await test_fallback_mechanism()
    
    # 测试角色提取功能
    character_result = test_character_extraction()
    
    # 输出最终总结
    print(f"\n{'='*60}")
    print("🎯 最终测试总结")
    print(f"{'='*60}")
    
    working_apis = sum(1 for status in api_results.values() if status)
    total_apis = len(api_results)
    
    print(f"API可用性: {working_apis}/{total_apis} 个API可用")
    print(f"故障转移: {'✅ 正常' if fallback_result else '❌ 异常'}")
    print(f"角色提取: {'✅ 正常' if character_result else '❌ 异常'}")
    
    # 问题诊断
    if not character_result:
        print(f"\n🔍 角色提取问题诊断:")
        if working_apis == 0:
            print("❌ 所有API都不可用，这是角色提取失败的主要原因")
            print("建议:")
            print("  1. 检查网络连接")
            print("  2. 验证API密钥是否有效")
            print("  3. 确认API服务是否正常")
        elif working_apis < total_apis:
            print("⚠️ 部分API不可用，但应该有备选方案")
            print("建议:")
            print("  1. 检查故障转移机制是否正常工作")
            print("  2. 确认角色提取是否使用了故障转移")
        else:
            print("🤔 所有API都可用，但角色提取仍然失败")
            print("建议:")
            print("  1. 检查角色提取的提示词格式")
            print("  2. 检查响应解析逻辑")
            print("  3. 查看详细的错误日志")
    
    if working_apis > 0 and character_result:
        print(f"\n🎉 测试通过！系统功能正常")
        return 0
    else:
        print(f"\n⚠️ 测试发现问题，需要进一步检查")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
